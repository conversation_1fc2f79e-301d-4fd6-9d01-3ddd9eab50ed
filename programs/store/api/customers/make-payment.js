import _ from 'lodash';
import axios from 'axios';
import {trim} from 'framework/helpers';
import {findInstallment} from '../checkout/utils';
import {integrations} from '../../../finance/methods/online-pos-receipts';

export default async function (app, store, request, response) {
    const {
        customerId,
        amount,
        amountUSD,
        exchangeRate,
        installmentCount,
        cardBrand,
        cardNumber,
        cardHolder,
        expireYear,
        expireMonth,
        cvv,
        selectedCardBrand
    } = request.body;

    // Get real card brand.
    if (typeof cardNumber !== 'string' || cardNumber.length < 16) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Invalid card number'
        });
    }
    const cardInfoResponse = await axios({
        method: 'post',
        url: 'https://api.entererp.com/v1/common/bin-detail',
        headers: {
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({cardNumber})
    });
    const realCardBrand = cardInfoResponse.data.cardBrandCode;
    if (cardBrand !== realCardBrand) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Provided card brand is not the same as real card brand!'
        });
    }

    // Get store currency to check its name
    const storeCurrency = await app.collection('kernel.currencies').findOne({
        _id: store.currencyId,
        $select: ['name'],
        $disableSoftDelete: true,
        $disableActiveCheck: true
    });

    let usdCurrency = null;
    if (amountUSD) {
        usdCurrency = await app.collection('kernel.currencies').findOne({
            name: 'USD',
            $select: ['_id'],
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });
    }

    let finalAmount = amount;
    let posAmount = amount; // POS'tan çekilecek TL tutarı
    let isUsingForeignCurrency = false;
    let currencyId = store.currencyId;
    let currencyRate = 1;

    if (amount && amountUSD && exchangeRate) {
        if (storeCurrency.name !== 'TRY' && amountUSD > 0) {
            finalAmount = amountUSD; // Muhasebe kaydında döviz tutarı
            posAmount = amount; // POS'tan TL olarak çekilecek tutar
            currencyId = usdCurrency._id;
            currencyRate = exchangeRate;
            isUsingForeignCurrency = true;
        } else {
            finalAmount = amount;
            posAmount = amount;
            currencyId = store.currencyId;
            currencyRate = 1;
        }
    } else if (amountUSD && !amount) {
        finalAmount = amountUSD; // Muhasebe kaydında döviz tutarı
        posAmount = amountUSD * (exchangeRate || 1); // POS'tan TL olarak çekilecek tutar
        currencyId = usdCurrency._id;
        currencyRate = exchangeRate || 1;
        isUsingForeignCurrency = true;
    }

    const installment = await findInstallment(
        app,
        store,
        cardBrand,
        installmentCount,
        posAmount, // POS'tan çekilecek TL tutarı için taksit hesaplama
        selectedCardBrand,
        customerId
    );

    // Get pos.
    const pos = await app.collection('accounting.pos').findOne({
        _id: installment.posId,
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    if (!pos || !pos.integrationType) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'POS not found!'
        });
    }

    // Prepare financial entry payload.
    const bankAccount = await app.collection('accounting.bank-accounts').findOne({
        _id: pos.bankAccountId,
        $disableBranchCheck: true
    });
    const paymentJournal = await app.collection('accounting.journals').findOne({
        _id: bankAccount.journalId,
        $disableBranchCheck: true,
        $select: ['_id']
    });
    const posJournal = await app.collection('accounting.journals').findOne({
        _id: pos.journalId,
        $disableBranchCheck: true,
        $select: ['shortDescription']
    });
    const now = app.datetime.local().startOf('day');
    const entry = {};
    entry.status = 'draft';
    entry.type = 'receipt';
    entry.documentType = 'pos';
    entry.journalId = pos.journalId;
    entry.paymentAccountId = paymentJournal._id;
    entry.posId = pos._id;
    entry.partnerType = 'customer';
    entry.partnerId = customerId;
    entry.amount = posAmount; // POS'tan çekilen TL tutarı
    entry.amountUSD = amountUSD;
    entry.baseTotal = posAmount; // POS'tan çekilen TL tutarı
    entry.dueDifference = installment.total - posAmount;
    entry.total = installment.total;
    entry.installmentCount = installmentCount;
    
    if (_.isFinite(installment.plusInstallmentCount)) {
        entry.plusInstallmentCount = installment.plusInstallmentCount;
    }
    
    entry.installmentAmount = installment.installmentAmount;
    entry.branchId = store.branchId;
    entry.currencyId = currencyId;
    entry.currencyRate = currencyRate;
    entry.exchangeRateUsed = exchangeRate;
    entry.isUsingForeignCurrency = isUsingForeignCurrency;
    entry.reference = '';
    entry.description = posJournal.shortDescription || app.translate('EnterStore online POS receipt.');
    entry.recordDate = app.datetime.local().toJSDate();
    entry.issueDate = now.toJSDate();
    entry.dueDate = now.toJSDate();
    if (pos.paymentOnSpecificDate) {
        const cutoffDate = pos.cutoffDate;

        if (now.day > cutoffDate) {
            entry.dueDate = now.plus({months: 1}).set({day: cutoffDate}).toJSDate();
        } else {
            entry.dueDate = now.set({day: cutoffDate}).toJSDate();
        }

        entry.dueDate = app.datetime
            .fromJSDate(entry.dueDate)
            .plus({months: entry.installmentCount - 1})
            .toJSDate();
    } else {
        if (pos.posRefund === 'lump-sum-payment') {
            const lumpSumPayment = pos.lumpSumPayments.find(c => c.installment === entry.installmentCount);

            if (!!lumpSumPayment) {
                entry.dueDate = now.plus({days: lumpSumPayment.refund || 0}).toJSDate();
            }
        } else {
            const installmentPayment = pos.installmentPayments.find(c => c.installment === entry.installmentCount);

            if (!!installmentPayment) {
                entry.dueDate = now.plus({days: installmentPayment.refund || 0}).toJSDate();
            }
        }
    }

    // Get user.
    const user = await app.collection('kernel.users').findOne({isRoot: true});

    // Get currency.
    const currency = await app.collection('kernel.currencies').findOne({
        _id: currencyId,
        $select: ['name'],
        $disableSoftDelete: true,
        $disableActiveCheck: true
    });

    // Prepare partner.
    const customer = await app.collection('kernel.partners').findOne({
        _id: customerId,
        $select: ['name', 'email', 'phone', 'address'],
        $disableSoftDelete: true,
        $disableActiveCheck: true
    });
    const partner = {
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        address: customer.address
    };

    // Get result
    const url = store.website || '';
    const data = {
        cardBrand,
        cardNumber,
        cardHolder,
        expireMonth,
        expireYear,
        cvv,
        amount: installment.total, // POS'tan çekilecek TL tutarı
        installmentCount,
        posId: pos._id,
        partnerId: customerId,
        currencyId: isUsingForeignCurrency ? usdCurrency._id : store.currencyId, // Muhasebe için döviz ID'si
        currencyRate: currencyRate,
        description: posJournal.shortDescription || app.translate('EnterStore online POS receipt.'),
        pos,
        partner,
        currency: isUsingForeignCurrency ? {_id: usdCurrency._id, name: 'USD'} : currency, // Muhasebe için döviz bilgisi
        storeId: store._id,
        branchId: store.branchId,
        tutar: posAmount, // POS'tan çekilen TL tutarı
        tutar_sbp: finalAmount, // Muhasebe kaydında döviz tutarı
        exchangeRateUsed: exchangeRate,
        isUsingForeignCurrency: isUsingForeignCurrency,
        evaluateAccountCurrency: isUsingForeignCurrency,
        globalCurrencyRate: 1,

        ...(pos.integrationType === 'paytr'
            ? {
                  okUrl: `${trim(url, '/')}/api/checkout/paytr-success`,
                  failUrl: `${trim(url, '/')}/api/checkout/paytr-error`,
                  frameUrl: `${trim(url, '/')}/api/checkout/paytr-frame-3d`
              }
            : {}),
        entryPayload: entry
    };

    const result = await integrations[pos.integrationType].charge(app, data, {user});

    return response.json({
        customerName: partner.name,
        cardBrandCode: installment.cardBrandCode,
        cardBrandName: installment.cardBrandName,
        cardBrandLogo: installment.cardBrandLogo,
        installmentCount: installment.installmentCount,
        plusInstallmentCount: installment.plusInstallmentCount,
        installmentRate: installment.installmentRate,
        installmentAmount: installment.installmentAmount,
        total: installment.total,
        amountTL: posAmount, // POS'tan çekilen TL tutarı
        amountUSD: finalAmount, // Muhasebe kaydında döviz tutarı
        exchangeRate: exchangeRate,
        currencyUsed: currencyId,
        isUsingForeignCurrency: isUsingForeignCurrency,
        ...result
    });
}
